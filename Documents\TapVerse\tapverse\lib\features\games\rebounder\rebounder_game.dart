import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class RebounderGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'rebounder',
    gameName: 'Rebounder',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const RebounderGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _RebounderGameContent();
  }


}

class _RebounderGameContent extends ConsumerStatefulWidget {
  const _RebounderGameContent();

  @override
  ConsumerState<_RebounderGameContent> createState() => _RebounderGameContentState();
}

class _RebounderGameContentState extends ConsumerState<_RebounderGameContent>
    with TickerProviderStateMixin {

  // Game state
  late AnimationController _ballController;
  late AnimationController _paddleController;

  // Ball physics
  Offset _ballPosition = const Offset(200, 300);
  Offset _ballVelocity = const Offset(150, -200); // Start moving up-left
  final double _ballSize = 24; // Made bigger

  // Paddle state
  double _paddleX = 150;
  late double _paddleY;
  double _paddleWidth = 100;
  final double _paddleHeight = 30; // Made thicker
  double _targetPaddleX = 150;

  // Game mechanics
  double _baseSpeed = 200; // pixels per second
  double _currentSpeed = 200; // current speed in pixels per second
  final double _speedIncrement = 5.0; // More noticeable speed increase
  int _paddleHits = 0;
  int _tokensEarned = 0;
  int _consecutiveHits = 0;

  // Ball trail for visual effect
  final List<Offset> _ballTrail = [];

  // Constants
  late double _gameWidth;
  late double _gameHeight;
  static const double _maxPaddleSpeed = 800; // Max paddle movement speed

  @override
  void initState() {
    super.initState();

    _ballController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _paddleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    // Initialize game dimensions
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeGameDimensions();
      _resetGame();
      _startPhysicsLoop();
    });

    // Listen for game state changes to reset when needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen(gameStateProvider(RebounderGame.gameConfig), (previous, next) {
        if (previous?.status != next.status && next.status == GameStatus.notStarted) {
          _resetGame();
        }
      });
    });
  }

  @override
  void dispose() {
    _ballController.dispose();
    _paddleController.dispose();
    super.dispose();
  }

  void _initializeGameDimensions() {
    final size = MediaQuery.of(context).size;
    setState(() {
      _gameWidth = size.width;
      _gameHeight = size.height - 60; // Account for top bar space
      _paddleY = _gameHeight - 100; // Position paddle higher from bottom
      _ballPosition = Offset(_gameWidth / 2, _gameHeight / 2);
    });
  }

  void _resetGame() {
    if (!mounted) return;
    setState(() {
      // Reset ball to center-bottom, moving upward
      _ballPosition = Offset(_gameWidth / 2, _gameHeight - 150);
      _ballVelocity = Offset(150, -200); // Start moving up-left

      // Reset paddle to center
      _paddleX = (_gameWidth - _paddleWidth) / 2;
      _targetPaddleX = _paddleX;

      // Reset game mechanics
      _currentSpeed = _baseSpeed; // Reset to base speed
      _paddleHits = 0;
      _tokensEarned = 0;
      _consecutiveHits = 0;
      _paddleWidth = 100; // Reset paddle width

      // Clear ball trail
      _ballTrail.clear();
    });
  }

  void _startPhysicsLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);

    void updatePhysics() {
      if (!mounted) return;

      final gameState = ref.read(gameStateProvider(RebounderGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, updatePhysics);
        return;
      }

      setState(() {
        // Update paddle position with smooth movement
        _updatePaddleMovement();

        // Update ball position with current speed
        final deltaTime = 1.0 / frameRate;
        _ballPosition = Offset(
          _ballPosition.dx + _ballVelocity.dx * deltaTime,
          _ballPosition.dy + _ballVelocity.dy * deltaTime,
        );

        // Update ball trail
        _updateBallTrail();

        // Check wall collisions (but NOT bottom wall)
        _checkWallCollisions();

        // Check paddle collision
        _checkPaddleCollision();

        // Check if ball missed paddle (game over)
        if (_ballPosition.dy > _gameHeight + 50) {
          _endGame();
        }
      });

      Future.delayed(frameDuration, updatePhysics);
    }

    updatePhysics();
  }

  void _updatePaddleMovement() {
    // Smooth paddle movement with speed limit
    final deltaX = _targetPaddleX - _paddleX;
    final maxMove = _maxPaddleSpeed / 60; // Max movement per frame

    if (deltaX.abs() > maxMove) {
      _paddleX += deltaX.sign * maxMove;
    } else {
      _paddleX = _targetPaddleX;
    }
  }

  void _updateBallTrail() {
    // Add current ball position to trail
    _ballTrail.add(_ballPosition);

    // Keep trail length manageable (last 10 positions)
    if (_ballTrail.length > 10) {
      _ballTrail.removeAt(0);
    }
  }

  void _checkWallCollisions() {
    // Custom collision detection for Rebounder - NO bottom wall bounce
    final radius = _ballSize / 2;
    bool collided = false;

    // Left wall collision (screen edge)
    if (_ballPosition.dx <= radius) {
      _ballPosition = Offset(radius, _ballPosition.dy);
      // Preserve current speed, just reverse X direction
      final currentDirection = _ballVelocity / _ballVelocity.distance;
      _ballVelocity = Offset(-currentDirection.dx * _currentSpeed, currentDirection.dy * _currentSpeed);
      collided = true;
    }

    // Right wall collision (screen edge)
    if (_ballPosition.dx >= _gameWidth - radius) {
      _ballPosition = Offset(_gameWidth - radius, _ballPosition.dy);
      // Preserve current speed, just reverse X direction
      final currentDirection = _ballVelocity / _ballVelocity.distance;
      _ballVelocity = Offset(-currentDirection.dx * _currentSpeed, currentDirection.dy * _currentSpeed);
      collided = true;
    }

    // Top wall collision (screen edge)
    if (_ballPosition.dy <= radius) {
      _ballPosition = Offset(_ballPosition.dx, radius);
      // Preserve current speed, just reverse Y direction
      final currentDirection = _ballVelocity / _ballVelocity.distance;
      _ballVelocity = Offset(currentDirection.dx * _currentSpeed, -currentDirection.dy * _currentSpeed);
      collided = true;
    }

    // NO bottom wall collision - ball should fall through

    if (collided) {
      _triggerBounceEffect();
    }
  }

  void _checkPaddleCollision() {
    final ballLeft = _ballPosition.dx - _ballSize / 2;
    final ballRight = _ballPosition.dx + _ballSize / 2;
    final ballTop = _ballPosition.dy - _ballSize / 2;
    final ballBottom = _ballPosition.dy + _ballSize / 2;

    final paddleLeft = _paddleX;
    final paddleRight = _paddleX + _paddleWidth;
    final paddleTop = _paddleY;
    final paddleBottom = _paddleY + _paddleHeight;

    // Check if ball is colliding with paddle
    if (ballRight >= paddleLeft &&
        ballLeft <= paddleRight &&
        ballBottom >= paddleTop &&
        ballTop <= paddleBottom &&
        _ballVelocity.dy > 0) { // Ball must be moving downward

      // Calculate hit position on paddle (for angle calculation)
      final hitPosition = (_ballPosition.dx - (_paddleX + _paddleWidth / 2)) / (_paddleWidth / 2);
      final clampedHitPosition = hitPosition.clamp(-1.0, 1.0);
      final angle = clampedHitPosition * pi / 3; // Max 60 degrees from center

      // Calculate new velocity based on hit position and current speed
      _ballVelocity = Offset(
        sin(angle) * _currentSpeed,
        -cos(angle) * _currentSpeed, // Always bounce upward
      );

      // Ensure ball is above paddle
      _ballPosition = Offset(_ballPosition.dx, paddleTop - _ballSize / 2);

      // Update game mechanics
      _paddleHits++;
      _consecutiveHits++;

      // Award tokens: 1 token per paddle hit
      int tokensThisHit = 1;

      // Bonus tokens every 20 consecutive hits
      if (_consecutiveHits % 20 == 0) {
        tokensThisHit += 5; // Bonus 5 tokens
      }

      _tokensEarned += tokensThisHit;

      // Increase speed by 5.0 pixels per second each hit
      _currentSpeed += _speedIncrement;

      // Shrink paddle after each hit instead of every 10 hits
      _paddleWidth = (_paddleWidth * 0.99).clamp(60.0, 100.0); // Shrink by 1% each hit, min width 60px

      // Update score (1 point per paddle hit)
      final gameNotifier = ref.read(gameStateProvider(RebounderGame.gameConfig).notifier);
      gameNotifier.addScore(1);

      // Trigger feedback
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);

      // Play animations
      _ballController.forward().then((_) => _ballController.reset());
      _paddleController.forward().then((_) => _paddleController.reset());
    }
  }

  void _endGame() {
    final gameNotifier = ref.read(gameStateProvider(RebounderGame.gameConfig).notifier);
    gameNotifier.endGame(reason: 'Ball fell off screen');
  }

  void _triggerBounceEffect() {
    // Trigger feedback for wall bounce
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.targetHit);
    _ballController.forward().then((_) => _ballController.reset());
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final gameState = ref.read(gameStateProvider(RebounderGame.gameConfig));
    if (!gameState.isPlaying) return;

    // Update target paddle position for smooth movement
    final newX = (details.localPosition.dx - _paddleWidth / 2).clamp(
      0.0,
      _gameWidth - _paddleWidth,
    );

    setState(() {
      _targetPaddleX = newX;
    });
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(RebounderGame.gameConfig));

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF1a1a2e), Color(0xFF16213e)],
        ),
      ),
      child: GestureDetector(
        onPanUpdate: _onPanUpdate,
        child: Stack(
          children: [
            // Ball trail
            _buildBallTrail(),

            // Ball
            _buildBall(),

            // Paddle
            _buildPaddle(),

            // Game info is now handled by the top bar

            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBallTrail() {
    return Stack(
      children: _ballTrail.asMap().entries.map((entry) {
        final index = entry.key;
        final position = entry.value;
        final opacity = (index + 1) / _ballTrail.length * 0.6; // Fade trail
        final size = _ballSize * (0.3 + (index / _ballTrail.length) * 0.7); // Shrink trail

        return Positioned(
          left: position.dx - size / 2,
          top: position.dy - size / 2,
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Color(0xFF00FFFF).withValues(alpha: opacity), // Cyan center
                  Color(0xFF0080FF).withValues(alpha: opacity * 0.7), // Blue middle
                  Color(0xFF0040FF).withValues(alpha: opacity * 0.4), // Darker blue edge
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildBall() {
    return Positioned(
      left: _ballPosition.dx - _ballSize / 2,
      top: _ballPosition.dy - _ballSize / 2,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.3).animate(_ballController),
        child: Container(
          width: _ballSize,
          height: _ballSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.cyan[300]!, Colors.blue[700]!],
              stops: const [0.3, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.cyan.withValues(alpha: 0.5),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaddle() {
    return Positioned(
      left: _paddleX,
      top: _paddleY,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.1).animate(_paddleController),
        child: Container(
          width: _paddleWidth,
          height: _paddleHeight,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.orange[400]!, Colors.red[600]!],
            ),
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withValues(alpha: 0.5),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 20,
      right: 20,
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: const Text(
            'Drag anywhere to move the paddle!\n'
            'Keep the ball from falling off the bottom.\n'
            'Ball gets faster with each hit.\n'
            'Earn 1 token per hit + 5 bonus every 20 hits!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }
}


